<template>
  <div class="map-container">
    <div id="map" ref="mapContainer"></div>

    <YearSelector
      :current-year="currentYear"
      :current-month="currentMonth"
      :current-layer-type="currentLayerType"
      @date-change="handleDateChange"
    />

    <!-- 缩放级别指示器 -->
    <div class="zoom-indicator" v-if="currentZoomLevel">
      <div class="zoom-level">缩放: {{ currentZoomLevel }}</div>
      <div class="display-mode">{{ getCurrentDisplayMode() }}</div>
      <!-- *** 新增: 总数统计显示 -->
      <div v-if="totalFeatureCount > 0" class="total-count-status">
        <div class="total-info">📊 数据统计</div>
        <div class="count-details">
          总计: {{ totalFeatureCount }} 个要素
          <br/>显示: {{ currentGeoData.length }} 个要素
        </div>
      </div>
      <!-- *** 新增: 懒加载状态指示器 -->
      <div v-if="currentZoomLevel >= 15" class="lazy-load-status">
        <div class="load-mode">📍 分片加载模式</div>
        <div v-if="isLazyLoading" class="loading-status">🔄 加载中...</div>
        <div v-else class="loading-status">✅ 就绪</div>
      </div>
    </div>

    <!-- 图层切换按钮 -->
    <div class="layer-switch">
      <button
        :class="['layer-btn', { active: currentLayerType === 'fengdian2024pg' }]"
        @click="switchLayer('fengdian2024pg')"
      >
        <span class="layer-icon">🌪️</span>
        <span class="layer-text">风电</span>
      </button>
      <button
        :class="['layer-btn', { active: currentLayerType === 'haiyangmuchang2024pg' }]"
        @click="switchLayer('haiyangmuchang2024pg')"
      >
        <span class="layer-icon">🌊</span>
        <span class="layer-text">海洋牧场</span>
      </button>
    </div>

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-indicator">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载数据...</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import YearSelector from './YearSelector.vue'

// 天地图API密钥
const TIANDITU_KEY = 'c71477a3f164d6938566310e4c5df556'

// 响应式数据
const mapContainer = ref(null)
const currentYear = ref(2024)
const currentMonth = ref(1)
const currentZoomLevel = ref(0)
const isLoading = ref(false)
const currentLayerType = ref('fengdian2024pg') // 当前图层类型: 'fengdian2024pg' 或 'haiyangmuchang2024pg'
const totalFeatureCount = ref(0) // *** 新增: 响应式的总要素数量
let map = null

// 防抖定时器
let updateDataTimer = null
const UPDATE_DELAY = 500 // 500ms 防抖延迟

// *** 新增: 分片懒加载相关变量
let isLazyLoading = ref(false) // 懒加载状态
const MIN_ZOOM_FOR_LAZY_LOADING = 15 // 启用懒加载的最小缩放级别
// *** 新增: 性能优化参数
const MAX_TOTAL_FEATURES = 5000 // 总最大要素数量

// 天地图图层URLs
const tiandituLayers = {
  // 矢量底图
  vec: `https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${TIANDITU_KEY}`,
  // 矢量注记
  cva: `https://t{s}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${TIANDITU_KEY}`,
  // 影像底图
  img: `https://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${TIANDITU_KEY}`,
  // 影像注记
  cia: `https://t{s}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${TIANDITU_KEY}`
}

// GeoServer WFS 配置 - 使用代理路径
const geoServerConfig = {
  wfsUrl: '/geoserver/ne/ows', // 使用代理路径，不需要完整的 URL
  layers: {
    fengdian2024pg: {
      typeName: 'ne:fengdian2024pg',
      title: '风电2024数据',
      outputFormat: 'application/json',
      version: '1.0.0',
      crs: 'EPSG:4490', // 中国大地坐标系2000
      bounds: [
        [18.0, 108.0], // 西南角 - 扩大范围覆盖整个广东省及周边
        [26.0, 118.0]  // 东北角
      ],
      maxFeatures: 50000, // *** 统一: 5W限制
      pageSize: 5000, // *** 新增: 分页大小
      geometryType: 'Point'
    },
    haiyangmuchang2024pg: {
      typeName: 'ne:haiyangmuchang2024pg',
      title: '海洋牧场2024数据',
      outputFormat: 'application/json',
      version: '1.0.0',
      crs: 'EPSG:4490', // 中国大地坐标系2000
      bounds: [
        [18.0, 108.0], // 西南角 - 扩大范围覆盖整个广东省及周边
        [26.0, 118.0]  // 东北角
      ],
      maxFeatures: 50000, // *** 统一: 5W限制
      pageSize: 5000, // *** 统一: 分页大小
      geometryType: 'Polygon'
    }
  }
}

// 初始化地图
const initMap = () => {
  if (!mapContainer.value) {
    console.error('地图容器未找到')
    return
  }
  // 确保容器有明确的尺寸
  mapContainer.value.style.width = '100%'
  mapContainer.value.style.height = 'calc(100vh - 140px)'

  // 创建地图实例 - 设置更大的显示范围
  map = L.map(mapContainer.value, {
    center: [22.0, 113.5], // 广东省中心坐标
    zoom: 8, // 更大的显示范围
    minZoom: 3,
    maxZoom: 18,
    zoomControl: true,
    preferCanvas: true, // *** 修改: 优先使用Canvas渲染器以提高性能
    renderer: L.canvas()  // *** 修改: 明确使用Canvas渲染器
  })
  // 添加天地图矢量底图（首选）
  const vecLayer = L.tileLayer(tiandituLayers.vec, {
    subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
    attribution: '&copy; 天地图',
    minZoom: 3,
    maxZoom: 18,
    tileSize: 256,
    zoomOffset: 0,
    crossOrigin: true
  })

  // 添加天地图矢量注记
  const cvaLayer = L.tileLayer(tiandituLayers.cva, {
    subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
    attribution: '&copy; 天地图',
    minZoom: 3,
    maxZoom: 18,
    tileSize: 256,
    zoomOffset: 0,
    crossOrigin: true
  })

  // 添加天地图影像底图
  const imgLayer = L.tileLayer(tiandituLayers.img, {
    subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
    attribution: '&copy; 天地图',
    minZoom: 3,
    maxZoom: 18,
    tileSize: 256,
    zoomOffset: 0,
    crossOrigin: true
  })

  // 添加天地图影像注记
  const ciaLayer = L.tileLayer(tiandituLayers.cia, {
    subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
    attribution: '&copy; 天地图',
    minZoom: 3,
    maxZoom: 18,
    tileSize: 256,
    zoomOffset: 0,
    crossOrigin: true
  })

  // 备用OpenStreetMap图层
  const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '&copy; OpenStreetMap contributors',
    minZoom: 3,
    maxZoom: 18,
    crossOrigin: true
  })

  // 组合天地图图层
  const tiandituVecGroup = L.layerGroup([vecLayer, cvaLayer])
  const tiandituImgGroup = L.layerGroup([imgLayer, ciaLayer])

  tiandituImgGroup.addTo(map)

  // 添加图层控制器
  const baseLayers = {
    "天地图影像": tiandituImgGroup,
    "天地图矢量": tiandituVecGroup,
    "OpenStreetMap": osmLayer
  }

  L.control.layers(baseLayers).addTo(map)

  // 监听图层切换事件
  map.on('baselayerchange', () => {
    // 重新计算地图尺寸以确保新图层正确显示
    setTimeout(() => {
      if (map) {
        map.invalidateSize()
      }
    }, 100)
  })

  // 监听天地图加载错误，自动切换到备用图层
  let imgErrorCount = 0
  let vecErrorCount = 0
  const maxErrors = 3

  imgLayer.on('tileerror', (e) => {
    imgErrorCount++
    console.warn(`天地图影像图层加载失败 (${imgErrorCount}/${maxErrors})`, e)

    if (imgErrorCount >= maxErrors) {
      console.warn('天地图影像加载失败次数过多，切换到矢量图层')
      map.removeLayer(tiandituImgGroup)
      tiandituVecGroup.addTo(map)
    }
  })

  vecLayer.on('tileerror', (e) => {
    vecErrorCount++
    console.warn(`天地图矢量图层加载失败 (${vecErrorCount}/${maxErrors})`, e)

    if (vecErrorCount >= maxErrors && imgErrorCount >= maxErrors) {
      console.warn('天地图所有图层加载失败，切换到OpenStreetMap')
      map.removeLayer(tiandituVecGroup)
      osmLayer.addTo(map)
    }
  })

  // 调整地图尺寸的函数
  const resizeMap = () => {
    if (map) {
      map.invalidateSize(true)
    }
  }

  // 多次尝试调整地图尺寸以确保正确布局
  setTimeout(resizeMap, 100)
  setTimeout(resizeMap, 500)

  // 地图准备好后，设置初始视图并加载数据
  map.whenReady(() => {
    resizeMap()
    const initialBounds = L.latLngBounds([
      [18.0, 108.0], // 西南角
      [26.0, 118.0]  // 东北角
    ])
    map.fitBounds(initialBounds, { padding: [20, 20] })
    loadInitialData()
  })

  // 监听缩放事件
  map.on('zoomend', () => {
    currentZoomLevel.value = map.getZoom()
    
    // *** 修改: 缩放时处理懒加载
    if (currentZoomLevel.value >= MIN_ZOOM_FOR_LAZY_LOADING) {
      // 清除低缩放级别的缓存数据
      clearLowZoomCache()
      loadCurrentViewData()
    } else {
      // 清除懒加载数据，回到全局数据模式
      clearLazyLoadedData()
      updateDataDisplay()
    }
  })

  // 监听移动事件
  map.on('moveend', () => {
    if (currentZoomLevel.value >= MIN_ZOOM_FOR_LAZY_LOADING) {
      // *** 修改: 高缩放级别时使用懒加载
      debouncedLazyLoad()
    } else {
      // 低缩放级别时使用原有逻辑
      debouncedUpdateData()
    }
  })

  // 初始化缩放级别
  currentZoomLevel.value = map.getZoom()
}

// 加载初始数据
const loadInitialData = async () => {
  await processAndDisplayData(2024, 1, false)
}

// 数据管理变量
let currentDataLayers = []
let currentGeoData = []
let aggregatedMarkers = []
// *** 新增: 懒加载相关的数据管理
let lazyLoadedLayers = [] // 懒加载的图层
let lazyLoadedData = new Map() // 按瓦片存储的懒加载数据

// *** 新增: 数据缓存机制
let dataCache = new Map() // 数据缓存
let countCache = new Map() // 总数缓存
const CACHE_EXPIRY_TIME = 5 * 60 * 1000 // 5分钟缓存过期时间



// *** 新增: 生成缓存键
const generateCacheKey = (year, month, bounds, layerType) => {
  const boundsStr = bounds ? `${bounds.getWest()}_${bounds.getSouth()}_${bounds.getEast()}_${bounds.getNorth()}` : 'no_bounds'
  return `${layerType}_${year}_${month}_${boundsStr}`
}

// *** 新增: 检查缓存
const getCachedData = (cacheKey) => {
  const cached = dataCache.get(cacheKey)
  if (cached && (Date.now() - cached.timestamp) < CACHE_EXPIRY_TIME) {
    console.log('使用缓存数据:', cacheKey)
    return cached.data
  }
  return null
}

// *** 新增: 设置缓存
const setCachedData = (cacheKey, data) => {
  dataCache.set(cacheKey, {
    data: data,
    timestamp: Date.now()
  })

  // 清理过期缓存
  if (dataCache.size > 20) {
    const now = Date.now()
    for (const [key, value] of dataCache.entries()) {
      if (now - value.timestamp > CACHE_EXPIRY_TIME) {
        dataCache.delete(key)
      }
    }
  }
}

// *** 修正: 获取要素总数 - 使用兼容的方法
const fetchFeatureCount = async (year, month, layerType = null) => {
  try {
    const layerKey = layerType || currentLayerType.value
    const layerConfig = geoServerConfig.layers[layerKey]

    // 检查总数缓存
    const countCacheKey = `count_${layerKey}_${year}_${month}`
    const cachedCount = getCachedData(countCacheKey)
    if (cachedCount !== null) {
      return cachedCount.totalFeatures || 0
    }

    // *** 修正: 使用兼容的方法获取总数
    // 方法1: 尝试使用 maxFeatures=0 (某些GeoServer版本支持)
    let params = new URLSearchParams({
      service: 'WFS',
      version: layerConfig.version,
      request: 'GetFeature',
      typeName: layerConfig.typeName,
      outputFormat: layerConfig.outputFormat,
      maxFeatures: '0' // 尝试只获取元数据
    })

    // 时间过滤
    if (year && month) {
      const timingSque = `${year}${month.toString().padStart(2, '0')}`
      params.append('cql_filter', `timingsequ='${timingSque}'`)
    }

    let url = `${geoServerConfig.wfsUrl}?${params.toString()}`
    console.log('获取总数 URL (方法1):', url)

    let response = await fetch(url, {
      headers: {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache'
      }
    })

    if (response.ok) {
      const data = await response.json()
      let count = data.totalFeatures || data.numberMatched || data.numberReturned

      if (count > 0) {
        // 缓存总数
        setCachedData(countCacheKey, { totalFeatures: count })
        console.log(`总要素数量 (方法1): ${count}`)
        return count
      }
    }

    // *** 方法2: 如果方法1失败，使用小批量请求推算
    console.log('方法1失败，尝试方法2: 小批量推算')
    params = new URLSearchParams({
      service: 'WFS',
      version: layerConfig.version,
      request: 'GetFeature',
      typeName: layerConfig.typeName,
      outputFormat: layerConfig.outputFormat,
      maxFeatures: '1000' // 获取1000个样本
    })

    // 时间过滤
    if (year && month) {
      const timingSque = `${year}${month.toString().padStart(2, '0')}`
      params.append('cql_filter', `timingsequ='${timingSque}'`)
    }

    url = `${geoServerConfig.wfsUrl}?${params.toString()}`
    console.log('获取总数 URL (方法2):', url)

    response = await fetch(url, {
      headers: {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP 错误! 状态: ${response.status}`)
    }

    const data = await response.json()
    const sampleCount = data.features ? data.features.length : 0
    let estimatedTotal = data.totalFeatures || data.numberMatched || sampleCount

    // 如果没有获取到总数，但获取到了样本数据，进行估算
    if (!estimatedTotal && sampleCount === 1000) {
      // 如果返回了1000个，说明可能还有更多，估算为较大值
      estimatedTotal = Math.max(sampleCount * 2, 5000) // 保守估算
      console.log(`使用估算总数: ${estimatedTotal}`)
    } else if (!estimatedTotal) {
      estimatedTotal = sampleCount
    }

    // 缓存总数
    setCachedData(countCacheKey, { totalFeatures: estimatedTotal })

    console.log(`总要素数量 (方法2): ${estimatedTotal}`)
    return estimatedTotal
  } catch (error) {
    console.error('获取要素总数时出错:', error)
    return 0
  }
}

// *** 优化: 从 GeoServer WFS 获取 JSON 数据 - 支持空间过滤、分页和缓存
const fetchWFSData = async (year, month, bounds = null, layerType = null, startIndex = 0) => {
  try {
    const layerKey = layerType || currentLayerType.value
    const layerConfig = geoServerConfig.layers[layerKey]

    // *** 新增: 检查缓存
    const cacheKey = generateCacheKey(year, month, bounds, layerKey)
    const cachedData = getCachedData(cacheKey)
    if (cachedData) {
      return cachedData
    }

    isLoading.value = true

    // *** 简化: 统一使用5W限制
    const zoomLevel = map ? map.getZoom() : 8
    const maxFeatures = layerConfig.maxFeatures // 直接使用配置的5W限制

    // 构建 WFS 请求 URL
    const params = new URLSearchParams({
      service: 'WFS',
      version: layerConfig.version,
      request: 'GetFeature',
      typeName: layerConfig.typeName,
      outputFormat: layerConfig.outputFormat,
      maxFeatures: maxFeatures.toString()
    })

    // *** 优化: 添加分页支持
    if (startIndex > 0) {
      params.append('startIndex', startIndex.toString())
    }

    // *** 优化: 构建CQL过滤器（包含时间和空间过滤）
    let cqlFilters = []

    // 时间过滤
    if (year && month) {
      const timingSque = `${year}${month.toString().padStart(2, '0')}`
      cqlFilters.push(`timingsequ='${timingSque}'`)
    }

    // *** 修正: 使用BBOX参数进行空间过滤（更高效）
    if (bounds && map) {
      const bbox = `${bounds.getWest()},${bounds.getSouth()},${bounds.getEast()},${bounds.getNorth()}`
      params.append('bbox', bbox)
      params.append('srsname', 'EPSG:4326')
      console.log(`使用BBOX空间过滤: ${bbox}`)
    }

    // 应用CQL过滤器
    if (cqlFilters.length > 0) {
      params.append('cql_filter', cqlFilters.join(' AND '))
      console.log(`CQL过滤器: ${cqlFilters.join(' AND ')}`)
    }

    const url = `${geoServerConfig.wfsUrl}?${params.toString()}`
    console.log(`WFS 请求 URL (缩放=${zoomLevel}, 最大要素=${maxFeatures}):`, url)

    // *** 新增: 添加请求超时和重试机制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 30000) // 30秒超时

    try {
      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Cache-Control': 'no-cache'
        }
      })
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP 错误! 状态: ${response.status}`)
      }

      const responseText = await response.text()

      if (responseText.trim().startsWith('<?xml') || responseText.trim().startsWith('<')) {
        console.error('服务器返回XML错误:', responseText)
        throw new Error('服务器返回XML错误，请检查图层名称和参数。')
      }

      const data = JSON.parse(responseText)

      // *** 新增: 设置缓存
      setCachedData(cacheKey, data)

      return data
    } catch (fetchError) {
      clearTimeout(timeoutId)
      if (fetchError.name === 'AbortError') {
        throw new Error('请求超时，请尝试缩小查询范围或降低缩放级别')
      }
      throw fetchError
    }
  } catch (error) {
    console.error('获取WFS数据时出错:', error)
    throw error
  } finally {
    isLoading.value = false
  }
}

// *** 优化: 处理和显示 GeoJSON 数据 - 先获取总数，再智能加载显示数据
const processAndDisplayData = async (year, month, useCurrentBounds = true) => {
  try {
    clearDataLayers()

    // *** 新增: 首先获取总要素数量（用于统计显示）
    console.log('正在获取总要素数量...')
    totalFeatureCount.value = await fetchFeatureCount(year, month)

    // *** 修正: 更智能的边界过滤策略
    const zoomLevel = map ? map.getZoom() : 8
    let bounds = null

    // 策略：只有在高缩放级别或明确要求时才使用边界过滤
    // 这样可以确保低缩放级别时获取完整的统计数据
    if (zoomLevel >= 12 || (useCurrentBounds && zoomLevel >= 10)) {
      bounds = map ? map.getBounds() : null
      console.log('使用空间边界过滤，减少数据传输')
    } else {
      console.log('使用全局数据加载，确保统计准确性')
    }

    console.log(`数据加载策略: 总数=${totalFeatureCount.value}, 缩放=${zoomLevel}, 使用边界过滤=${!!bounds}`)

    // 加载显示数据（可能是部分数据）
    const geoJsonData = await fetchWFSData(year, month, bounds)
    currentGeoData = geoJsonData && geoJsonData.features ? geoJsonData.features : []

    console.log(`显示数据加载完成: 显示 ${currentGeoData.length} 个要素，总计 ${totalFeatureCount.value} 个要素`)
    updateDataDisplay()

  } catch (error) {
    console.error('数据处理错误:', error)
    currentGeoData = []
    totalFeatureCount.value = 0
  }
}

// 清除所有数据图层
const clearDataLayers = () => {
  currentDataLayers.forEach(layer => map.removeLayer(layer))
  currentDataLayers = []
  aggregatedMarkers.forEach(marker => map.removeLayer(marker))
  aggregatedMarkers = []
}

// *** 修正: 加载当前视窗数据 - 添加错误处理
const loadCurrentViewData = async () => {
  try {
    if (!map || isLazyLoading.value) return

    const currentBounds = map.getBounds()
    const zoom = map.getZoom()

    // 如果没有缓存数据，先加载全部数据
    if (!currentGeoData || !Array.isArray(currentGeoData) || !currentGeoData.length) {
      console.log('没有缓存数据，先加载全部数据')
      isLazyLoading.value = true
      try {
        await processAndDisplayData(currentYear.value, currentMonth.value, false)
      } finally {
        isLazyLoading.value = false
      }

      if (!currentGeoData || !currentGeoData.length) {
        console.log('无数据可显示')
        return
      }
    }

    // 过滤有效数据
    const validCacheData = currentGeoData.filter(feature =>
      feature && feature.geometry && feature.geometry.type
    )

    console.log(`分片模式: 从 ${currentGeoData.length} 个缓存要素中筛选当前视窗数据 (有效: ${validCacheData.length})`)

    // 根据缩放级别调整瓦片大小
    const currentTileSize = getOptimalTileSize(zoom)

    // 获取当前视窗范围内的要素
    const viewportFeatures = filterFeaturesInBounds(validCacheData, currentBounds)

    console.log(`视窗内找到 ${viewportFeatures.length} 个要素`)

    // 更新懒加载显示
    updateLazyLoadedDisplayFromCache(viewportFeatures)
  } catch (error) {
    console.error('加载当前视窗数据时出错:', error)
    isLazyLoading.value = false
  }
}

// *** 修正: 从缓存数据中筛选指定边界内的要素 - 添加空值检查
const filterFeaturesInBounds = (features, bounds) => {
  if (!features || !Array.isArray(features) || !bounds) {
    console.warn('filterFeaturesInBounds: 无效参数', { features: !!features, bounds: !!bounds })
    return []
  }

  return features.filter(feature => {
    // 检查要素和几何对象是否存在
    if (!feature || !feature.geometry || !feature.geometry.type) {
      return false
    }

    const { geometry } = feature

    try {
      if (geometry.type === 'Point') {
        if (!geometry.coordinates || geometry.coordinates.length < 2) {
          return false
        }
        const [lng, lat] = geometry.coordinates

        // 检查坐标有效性
        if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
          return false
        }

        return bounds.contains([lat, lng])
      } else if (geometry.type === 'Polygon' || geometry.type === 'MultiPolygon') {
        if (!geometry.coordinates || geometry.coordinates.length === 0) {
          return false
        }

        // 对于多边形，检查其中心点是否在边界内
        try {
          const geoJsonLayer = L.geoJSON(feature)
          const center = geoJsonLayer.getBounds().getCenter()
          return bounds.contains(center)
        } catch (error) {
          console.warn('多边形处理失败:', error, feature)
          return false
        }
      }
    } catch (error) {
      console.warn('要素边界检查失败:', error, feature)
      return false
    }

    return false
  })
}

// *** 新增: 根据缩放级别获取最佳瓦片大小（用于性能优化参考）
const getOptimalTileSize = (zoom) => {
  if (zoom >= 18) return 0.005  // ~500m
  if (zoom >= 17) return 0.01   // ~1km
  if (zoom >= 16) return 0.02   // ~2km
  if (zoom >= 15) return 0.05   // ~5km
  return 0.1 // ~10km
}

// *** 修改: 更新懒加载显示（从缓存数据）
const updateLazyLoadedDisplayFromCache = (features) => {
  if (!map) return
  
  // 清除现有的懒加载图层
  clearLazyLoadedLayers()
  
  const totalFeatures = features.length
  console.log(`准备渲染 ${totalFeatures} 个视窗内要素`)
  
  // 性能优化 - 限制渲染数量
  const maxFeaturesToRender = Math.min(totalFeatures, MAX_TOTAL_FEATURES)
  const featuresToRender = features.slice(0, maxFeaturesToRender)
  
  if (totalFeatures > MAX_TOTAL_FEATURES) {
    console.warn(`要素过多 (${totalFeatures})，只渲染前 ${MAX_TOTAL_FEATURES} 个`)
  }
  
  // 渲染要素
  const layers = renderTileFeatures(featuresToRender)
  lazyLoadedLayers = layers
  
  console.log(`分片懒加载: 成功渲染 ${featuresToRender.length} 个要素`)
}

// *** 新增: 渲染瓦片要素
const renderTileFeatures = (features) => {
  const layers = []
  
  // *** 修改: 移除重复的限制逻辑，由调用方控制要素数量
  features.forEach(feature => {
    const { geometry, properties } = feature
    let layer = null

    if (geometry.type === 'Point') {
      const [lng, lat] = geometry.coordinates
      layer = L.marker([lat, lng], {
        icon: createWindTurbineIcon(30)
      }).addTo(map)
    } else if (geometry.type === 'Polygon' || geometry.type === 'MultiPolygon') {
      layer = L.geoJSON(feature, {
        style: {
          fillColor: getPolygonColor(properties),
          color: '#2196F3',
          weight: 1,
          opacity: 0.8,
          fillOpacity: 0.3
        }
      }).addTo(map)
    }

    if (layer) {
      layer.bindPopup(createDetailedFeaturePopup(properties))
      layers.push(layer)
    }
  })
  
  return layers
}

// *** 新增: 清除懒加载图层
const clearLazyLoadedLayers = () => {
  lazyLoadedLayers.forEach(layer => {
    if (map.hasLayer(layer)) {
      map.removeLayer(layer)
    }
  })
  lazyLoadedLayers = []
}

// *** 修改: 清除懒加载数据
const clearLazyLoadedData = () => {
  clearLazyLoadedLayers()
  // 不清除 currentGeoData，保持缓存
}

// *** 删除: 移除不再需要的瓦片相关变量和函数
// let loadedTiles = new Map()
// let lazyLoadedData = new Map()
// const MAX_CACHED_TILES = 50
// getTileBounds, cleanupTileCache 等函数

// *** 新增: 清除低缩放级别缓存
const clearLowZoomCache = () => {
  // 在进入高缩放级别时，清除原有的全局数据显示
  clearDataLayers()
}

// *** 新增: 懒加载防抖函数
const debouncedLazyLoad = () => {
  clearTimeout(updateDataTimer)
  updateDataTimer = setTimeout(loadCurrentViewData, UPDATE_DELAY)
}

// 切换图层类型
const switchLayer = async (layerType) => {
  if (layerType === currentLayerType.value) return
  console.log(`切换图层: ${currentLayerType.value} -> ${layerType}`)

  // 清除所有现有数据和显示
  clearDataLayers()
  clearLazyLoadedData()
  currentGeoData = [] // *** 修改: 切换图层时清空缓存，重新获取数据
  currentLayerType.value = layerType

  // 重新获取新图层的数据
  await processAndDisplayData(currentYear.value, currentMonth.value, false)
  
  // 如果当前是高缩放级别，立即更新懒加载显示
  if (currentZoomLevel.value >= MIN_ZOOM_FOR_LAZY_LOADING) {
    loadCurrentViewData()
  }
}

// 防抖更新函数（用于地图移动）
const debouncedUpdateData = () => {
  clearTimeout(updateDataTimer)
  updateDataTimer = setTimeout(updateDataDisplay, UPDATE_DELAY)
}

// *** 修正: 根据缩放级别更新数据显示 - 添加错误处理
const updateDataDisplay = () => {
  try {
    if (!map) {
      clearDataLayers()
      return
    }

    const zoomLevel = map.getZoom()

    // *** 修改: 高缩放级别时不处理全局数据显示
    if (zoomLevel >= MIN_ZOOM_FOR_LAZY_LOADING) {
      console.log('高缩放级别，使用懒加载模式')
      return // 懒加载模式下不使用此函数
    }

    if (!currentGeoData || !Array.isArray(currentGeoData) || !currentGeoData.length) {
      clearDataLayers()
      console.log('没有有效数据可显示')
      return
    }

    clearDataLayers()
    console.log(`更新显示: 缩放=${zoomLevel}, 数据量=${currentGeoData.length}`)

    // 过滤有效数据
    const validData = currentGeoData.filter(feature =>
      feature && feature.geometry && feature.geometry.type
    )

    if (!validData.length) {
      console.warn('没有有效的几何数据')
      return
    }

    if (zoomLevel >= 13 && validData.length <= 20) {
      console.log('显示详细要素（图标/多边形）')
      showDetailedFeatures()
    } else {
      console.log('显示聚合数据（数字标记）')
      showAggregatedData(zoomLevel)
    }
  } catch (error) {
    console.error('更新数据显示时出错:', error)
    clearDataLayers()
  }
}

// 显示聚合数据（数字标记）
const showAggregatedData = (zoomLevel) => {
  const aggregatedData = aggregateFeaturesByZoomLevel(currentGeoData, zoomLevel)

  aggregatedData.forEach(group => {
    const marker = L.marker([group.lat, group.lng], {
      icon: createAggregatedIcon(group, zoomLevel)
    }).addTo(map)

    marker.on('click', () => {
      const nextZoom = Math.min(zoomLevel + 2, 18)
      map.setView([group.lat, group.lng], nextZoom)
    })

    marker.bindPopup(`
      <div class="feature-popup">
        <h4>聚合区域</h4>
        <p><strong>显示数量:</strong> ${group.count}</p>
        <p><strong>总计数量:</strong> ${totalFeatureCount.value}</p>
        <p><strong>级别:</strong> ${getLevelName(zoomLevel)}</p>
        <p><em>点击放大查看详细信息</em></p>
      </div>
    `)
    aggregatedMarkers.push(marker)
  })
}

// *** 修正: 显示详细要素 - 添加空值检查
const showDetailedFeatures = () => {
  if (!currentGeoData || !Array.isArray(currentGeoData)) {
    console.warn('没有有效的数据可显示')
    return
  }

  currentGeoData.forEach(feature => {
    // 检查要素是否有效
    if (!feature || !feature.geometry || !feature.geometry.type) {
      console.warn('跳过无效要素:', feature)
      return
    }

    const { geometry, properties } = feature
    let layer = null

    try {
      if (geometry.type === 'Point') {
        if (!geometry.coordinates || geometry.coordinates.length < 2) {
          console.warn('跳过无效点坐标:', geometry.coordinates)
          return
        }
        const [lng, lat] = geometry.coordinates

        // 检查坐标有效性
        if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
          console.warn('跳过无效坐标:', { lat, lng })
          return
        }

        layer = L.marker([lat, lng], {
          icon: createWindTurbineIcon(50)
        }).addTo(map)
      } else if (geometry.type === 'Polygon' || geometry.type === 'MultiPolygon') {
        if (!geometry.coordinates || geometry.coordinates.length === 0) {
          console.warn('跳过无效面坐标:', geometry.coordinates)
          return
        }

        layer = L.geoJSON(feature, {
          style: {
            fillColor: getPolygonColor(properties),
            color: '#2196F3',
            weight: 2,
            opacity: 0.8,
            fillOpacity: 0.4
          }
        }).addTo(map)
      }

      if (layer) {
        layer.bindPopup(createDetailedFeaturePopup(properties || {}))
        currentDataLayers.push(layer)
      }
    } catch (error) {
      console.warn('处理要素时出错:', error, feature)
    }
  })
}

// *** 修正: 按缩放级别聚合要素 - 添加空值检查
const aggregateFeaturesByZoomLevel = (features, zoomLevel) => {
  const gridSize = getGridSizeByZoomLevel(zoomLevel)
  const groups = new Map()

  features.forEach(feature => {
    // *** 修正: 检查要素和几何对象是否存在
    if (!feature || !feature.geometry || !feature.geometry.type) {
      console.warn('跳过无效要素:', feature)
      return
    }

    let lat, lng
    try {
      if (feature.geometry.type === 'Point') {
        if (!feature.geometry.coordinates || feature.geometry.coordinates.length < 2) {
          console.warn('跳过无效点要素:', feature)
          return
        }
        [lng, lat] = feature.geometry.coordinates
      } else if (['Polygon', 'MultiPolygon'].includes(feature.geometry.type)) {
        if (!feature.geometry.coordinates || feature.geometry.coordinates.length === 0) {
          console.warn('跳过无效面要素:', feature)
          return
        }
        const center = L.geoJSON(feature).getBounds().getCenter()
        lat = center.lat
        lng = center.lng
      } else {
        console.warn('跳过不支持的几何类型:', feature.geometry.type)
        return
      }

      // 检查坐标是否有效
      if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
        console.warn('跳过无效坐标:', { lat, lng })
        return
      }
    } catch (error) {
      console.warn('处理要素时出错:', error, feature)
      return
    }

    const gridLat = Math.floor(lat / gridSize) * gridSize
    const gridLng = Math.floor(lng / gridSize) * gridSize
    const key = `${gridLat}_${gridLng}`

    if (!groups.has(key)) {
      groups.set(key, { lat: 0, lng: 0, count: 0, totalLat: 0, totalLng: 0 })
    }

    const group = groups.get(key)
    group.count++
    group.totalLat += lat
    group.totalLng += lng
  })

  return Array.from(groups.values()).map(g => ({
    count: g.count,
    lat: g.totalLat / g.count,
    lng: g.totalLng / g.count
  }))
}

// 处理窗口大小调整
const handleResize = () => {
  if (map) {
    setTimeout(() => map.invalidateSize(), 100)
  }
}

// 创建详细要素弹窗内容
const createDetailedFeaturePopup = (properties) => {
  let content = `<div class="feature-popup detailed-popup"><h3>${properties.entityname || properties.EntityName || '要素详情'}</h3>`
  for (const key in properties) {
    if (properties[key] !== null && properties[key] !== '' && key !== 'geom') {
      content += `<p><strong>${getFieldDisplayName(key)}:</strong> ${properties[key]}</p>`
    }
  }
  return content + '</div>'
}

// *** 修正: 获取字段的显示名称 - 匹配数据库结构
const getFieldDisplayName = (fieldName) => {
  const names = {
    // 基础信息
    'gid': 'ID',
    'entityname': '实体名称',
    'entityid': '实体ID',
    'locationid': '位置ID',
    'classid': '类别ID',
    'classname': '类别名称',
    'modelid': '模型ID',
    'timingsequ': '时间序列',

    // 海洋牧场特有字段
    'loadingtim': '加载时间',
    'area': '面积',
    'seaareatyp': '海域类型',
    'producttyp': '产品类型',
    'regionalis': '区域信息',
    'regional_1': '区域信息1',
    'residences': '居住地',
    'alias': '别名',
    'borntime': '建立时间',
    'endingtime': '结束时间',
    'updatestat': '更新状态',
    'updatedata': '更新日期',
    'datasource': '数据源',
    'exitstatus': '退出状态',
    'constructs': '建设状态',
    'material': '材料',
    'owners': '所有者',
    'users': '使用者',
    '养殖设': '养殖设施',
    'area_geo': '几何面积',
    'shape_leng': '形状长度',
    'shape_area': '形状面积',

    // 通用字段
    'Capacity': '容量',
    'Height': '塔高',
    'Status': '状态'
  }
  return names[fieldName] || fieldName
}

// 获取多边形的颜色
const getPolygonColor = (properties) => {
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
  const id = properties.id || properties.ID || properties.FID || 0
  return colors[id % colors.length]
}

// 创建风车图标
const createWindTurbineIcon = (size = 24) => {
  return L.icon({
    iconUrl: new URL('./icons/windmill.svg', import.meta.url).href,
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2],
    popupAnchor: [0, -size / 2]
  })
}

// 创建聚合图标（显示数字）
const createAggregatedIcon = (group, zoomLevel) => {
  const size = getSizeByZoomLevel(zoomLevel)
  const color = getColorByZoomLevel(zoomLevel)
  return L.divIcon({
    className: 'aggregated-marker',
    html: `<div class="aggregated-marker-content" style="background-color: ${color}; width: ${size}px; height: ${size}px;">${group.count}</div>`,
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2]
  })
}

// 根据缩放级别获取图标大小
const getSizeByZoomLevel = (zoom) => {
  if (zoom <= 6) return 50; if (zoom <= 8) return 45; if (zoom <= 10) return 40;
  if (zoom <= 12) return 35; if (zoom <= 14) return 30; return 25;
}

// 根据缩放级别获取颜色
const getColorByZoomLevel = (zoom) => {
  if (zoom <= 6) return '#e74c3c'; if (zoom <= 8) return '#f39c12'; if (zoom <= 10) return '#f1c40f';
  if (zoom <= 12) return '#2ecc71'; if (zoom <= 14) return '#3498db'; return '#9b59b6';
}

// 根据缩放级别获取聚合的网格大小
const getGridSizeByZoomLevel = (zoom) => {
  if (zoom <= 6) return 2.0; if (zoom <= 8) return 1.0; if (zoom <= 10) return 0.5;
  if (zoom <= 12) return 0.2; if (zoom <= 14) return 0.1; return 0.05;
}

// 根据缩放获取级别名称
const getLevelName = (zoom) => {
  if (zoom <= 6) return '省级聚合'; if (zoom <= 8) return '市级聚合';
  if (zoom <= 10) return '区县级聚合'; if (zoom <= 12) return '镇级聚合';
  return '详细视图';
}

// *** 修改: 获取当前显示模式的描述 - 显示正确的统计信息
const getCurrentDisplayMode = () => {
  const zoom = currentZoomLevel.value
  const layerTitle = geoServerConfig.layers[currentLayerType.value]?.title || '数据'
  const displayCount = currentGeoData.length
  const total = totalFeatureCount.value

  // 构建基础显示信息
  let modeInfo = ''
  if (zoom >= MIN_ZOOM_FOR_LAZY_LOADING) {
    modeInfo = '分片懒加载'
  } else if (zoom >= 13 && displayCount <= 20) {
    modeInfo = '详细视图'
  } else {
    modeInfo = getLevelName(zoom)
  }

  // 添加统计信息
  if (total > 0) {
    if (displayCount < total) {
      return `${layerTitle} - ${modeInfo} (显示 ${displayCount}/${total})`
    } else {
      return `${layerTitle} - ${modeInfo} (共 ${total} 个)`
    }
  } else {
    return `${layerTitle} - ${modeInfo}`
  }
}

// 处理日期选择器的日期变化
const handleDateChange = async ({ year, month }) => {
  currentYear.value = year
  currentMonth.value = month
  
  // 清除现有显示
  clearLazyLoadedData()
  
  // *** 修改: 日期变化时清空缓存，重新获取对应日期的数据
  currentGeoData = []
  
  // 重新获取新日期的数据
  await processAndDisplayData(year, month, false)
  
  // 如果当前是高缩放级别，立即更新懒加载显示
  if (currentZoomLevel.value >= MIN_ZOOM_FOR_LAZY_LOADING) {
    loadCurrentViewData()
  }
}

// 组件生命周期钩子
onMounted(() => {
  initMap()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  clearTimeout(updateDataTimer)
  clearLazyLoadedData() // *** 新增: 清理懒加载数据
  if (map) {
    map.remove()
    map = null
  }
})
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 140px);
  overflow: hidden;
}

#map {
  width: 100% !important;
  height: 100% !important;
  position: relative;
  background: #f0f0f0;
}

:deep(.leaflet-container) {
  font-size: 12px;
}

/* 缩放指示器样式 */
.zoom-indicator {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 2000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.zoom-level {
  font-weight: bold;
  margin-bottom: 2px;
}

.display-mode {
  font-size: 10px;
  opacity: 0.8;
}

/* *** 新增: 总数统计状态指示器样式 */
.total-count-status {
  margin-top: 4px;
  padding-top: 4px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.total-info {
  font-size: 10px;
  color: #FF9800;
  font-weight: bold;
}

.count-details {
  font-size: 9px;
  opacity: 0.9;
  margin-top: 2px;
  line-height: 1.3;
}

/* *** 新增: 懒加载状态指示器样式 */
.lazy-load-status {
  margin-top: 4px;
  padding-top: 4px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.load-mode {
  font-size: 10px;
  color: #4CAF50;
  font-weight: bold;
}

.loading-status {
  font-size: 9px;
  opacity: 0.9;
  margin-top: 2px;
}

/* 加载指示器样式 */
.loading-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 8px;
  z-index: 3000;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
}

/* 聚合标记样式 */
:deep(.aggregated-marker) {
  background: transparent !important;
  border: none !important;
}

:deep(.aggregated-marker-content) {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
  transition: all 0.3s ease;
}

:deep(.aggregated-marker-content:hover) {
  transform: scale(1.1);
}

/* 弹窗样式 */
:deep(.leaflet-popup-content) {
  margin: 8px 12px;
  line-height: 1.4;
}

:deep(.feature-popup) {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  max-width: 300px;
}

:deep(.feature-popup h3) {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c5aa0;
}

:deep(.feature-popup p) {
  margin: 4px 0;
  font-size: 12px;
}

:deep(.feature-popup strong) {
  font-weight: 600;
}

/* 图层切换按钮样式 */
.layer-switch {
  position: fixed;
  top: 20px; /* 恢复到右上角 */
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1000;
}

.layer-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px; /* 减小内边距 */
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e0e0e0;
  border-radius: 20px; /* 减小圆角 */
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px; /* 减小字体 */
  font-weight: 500;
  color: #666;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.layer-btn:hover {
  border-color: #2196F3;
  transform: translateY(-2px);
}

.layer-btn.active {
  background: #2196F3;
  border-color: #2196F3;
  color: white;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.layer-icon {
  font-size: 14px; /* 减小图标尺寸 */
}

.layer-text {
  font-size: 11px; /* 减小文字尺寸 */
  font-weight: 600;
}
</style>
